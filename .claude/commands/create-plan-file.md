# 任务：创建并保存一个版本化的计划文件

## Usage
```
/create-plan-file <name>
```

## 1. 角色与目标
你将扮演一位 **文件系统自动化机器人**，你的核心目标是接收一个**任务标识（`{name}`）**和一段对话大纲，自动确定下一个版本号，并根据该大纲在指定的任务目录中创建一个格式正确的 Markdown 计划文件。

## 2. 背景与上下文
为了将先前对话中形成的共识和计划（体现为一份对话大纲）正式地存档，用户正在为不同的任务维护版本化的计划文档系列。此任务旨在将指定任务的大纲自动化地生成为序列中的下一个计划文件，确保沟通结果被妥善记录和版本化。**任务标识（`{name}`）**和**对话大纲**将作为此任务的主要输入内容。

## 3. 关键步骤
在你的执行过程中，请遵循以下内部步骤来完成任务：
1.  **确定并准备目标目录**:
    - 基于输入的任务标识 `{name}`，构建目标目录路径：`.claude/plan-task/{name}/`。
    - 检查该目录是否存在。如果不存在，则递归创建它。
2.  **确定下一个版本号**:
    - 扫描第1步中确定的目标目录，查找所有匹配 `plan-{name}-*.md` 模式的文件。
    - 识别出现有的最高版本号。
    - 将最高的版本号加一，作为新的版本号。如果不存在任何计划文件，则从 `001` 开始。
    - 确保版本号始终为三位数，不足则用前导零填充（例如：`001`, `002`, `010`）。
3.  **组装文件内容**:
    - 基于任务标识和新版本号构建文件名，格式为 `plan-{name}-<新版本号>.md`。
    - 准备要写入文件的完整内容，结构如下：
        - 一级标题：`# Plan {name} v<新版本号>`
        - 一个空行
        - 作为输入提供的完整对话大纲
        - 一个空行
        - 页脚：`Created: <当前UTC时间戳，格式为ISO 8601>`
4.  **创建并保存文件**: 将组装好的内容写入到在目标目录中新建的文件。
5.  **输出确认信息**: 文件成功保存后，生成一条简洁的确认消息，告知用户操作已完成。

## 4. 输出要求
- **格式**: 单行纯文本。
- **风格**: 简洁、事实明确、信息完整。
- **约束**:
    - 这是一个真实的文件创建操作，而不是模拟。
    - 必须准确计算和格式化版本号。
    - **最终输出**: 你的最终回复应仅包含确认消息本身，例如 `File '.claude/plan-task/{name}/plan-{name}-001.md' created successfully.`。不得包含任何文件内容、执行步骤或其他无关的解释。