# QA验证失败报告 - GetGjkLessonTag

## 函数名 (FuncName)
`GetGjkLessonTag`

## 问题描述 (Description)
Go 实现中缺少了对 `studyPlanTag` 值的转换逻辑。PHP 实现会将数值（1,2,3）转换为对应的文字描述（"必看","选看","不看"），但 Go 实现中直接输出了数值，导致输出格式不一致。

## 复现路径/代码片段 (Reproduce Steps/Code Snippet)

### PHP Snippet (正确实现)
```php
// 在 getGjkLessonTag 函数中设置数值
if (isset($this->lessonTagData[$row['lessonId']])) {
    $row['studyPlanTag'] = $this->lessonTagData[$row['lessonId']]; // 设置为数值 1,2,3
    $row['canEditPlan']  = 1;
}

// 在导出逻辑中进行值转换 (PerformanceV1.php 第289-291行)
if ($one['prop'] == 'studyPlanTag') {
    $row[] = AssistantDesk_Data_GjkData::MAP_GJK_TAG[$val[$one['prop']]]; // 转换为文字
    continue;
}

// AssistantDesk_Data_GjkData::MAP_GJK_TAG 定义
const MAP_GJK_TAG = [
    self::GJK_TAG_MUST   => '必看',    // 1 => '必看'
    self::GJK_TAG_OPTION => '选看',    // 2 => '选看'
    self::GJK_TAG_NO     => '不看',    // 3 => '不看'
];
```

### Go Snippet (有问题的实现)
```go
// 在 GetGjkLessonTag 函数中直接输出数值，缺少转换
if tiers, exists := lessonTagMap[lessonID]; exists {
    studyPlanTag = tiers  // 直接使用数值 1,2,3
    canEditPlan = 1
}

// 输出结果 - 缺少值转换
_ = s.AddOutputStudent(ctx, lessonID, "canEditPlan", canEditPlan)
_ = s.AddOutputStudent(ctx, lessonID, "studyPlanTag", studyPlanTag) // 直接输出数值，应该转换为文字
```

## 数据一致性影响
- **PHP 输出**: `studyPlanTag` 字段值为 `"必看"`, `"选看"`, `"不看"`
- **Go 输出**: `studyPlanTag` 字段值为 `1`, `2`, `3`
- **结果**: 前端或下游系统接收到的数据格式不一致，可能导致显示错误

## 建议修复方案 (Suggested Fix)
在 Go 实现中添加值转换逻辑：

```go
// 在 GetGjkLessonTag 函数中添加转换逻辑
import "deskcrm/consts"

func (s *Format) GetGjkLessonTag(ctx *gin.Context) (err error) {
    // ... 现有逻辑 ...
    
    for _, lessonID := range s.param.LessonIDs {
        canEditPlan := 0
        studyPlanTag := 0
        studyPlanTagText := ""  // 添加文字版本
        
        // ... 现有逻辑 ...
        
        if playType != dal.PLAY_TYPE_LUBOKE {
            canEditPlan = 0
            studyPlanTag = 0
            studyPlanTagText = ""
        } else {
            if tiers, exists := lessonTagMap[lessonID]; exists {
                studyPlanTag = tiers
                canEditPlan = 1
                // 添加值转换逻辑
                if tagText, ok := consts.GJKTagMap[tiers]; ok {
                    studyPlanTagText = tagText
                }
            } else {
                canEditPlan = 0
                studyPlanTag = 0
                studyPlanTagText = ""
            }
        }
        
        // 输出转换后的文字值，保持与PHP一致
        _ = s.AddOutputStudent(ctx, lessonID, "canEditPlan", canEditPlan)
        _ = s.AddOutputStudent(ctx, lessonID, "studyPlanTag", studyPlanTagText) // 输出文字而非数值
    }
    
    // ... 其余逻辑 ...
}
```

## 验证状态
❌ **验证失败** - 需要修复值转换逻辑后重新验证

## 优先级
**高** - 影响数据输出格式一致性，可能导致前端显示错误
