# QA验证失败报告 - GetLpcAttendStatus

## 函数名 (FuncName)
`GetLpcAttendStatus`

## 问题描述 (Description)
Go 实现中的状态常量值与 PHP 实现不匹配，导致输出的状态值完全不同。这是一个严重的数据一致性问题，会导致前端或下游系统接收到错误的状态值。

## 复现路径/代码片段 (Reproduce Steps/Code Snippet)

### PHP Snippet (正确实现)
```php
// PHP 常量定义 (PerformanceV1.php 第23-25行)
const UNDONE_STATUS  = 1;  // 未完成
const SUCCESS_STATUS = 2;  // 已完成  
const WAIT_STATUS    = 3;  // 未开始

// PHP 状态判断逻辑 (PerformanceV1.php 第2052行)
$item['attendStatus'] = $lessonStartTime > time() ? self::WAIT_STATUS : ($isAttend > 0 ? self::SUCCESS_STATUS : self::UNDONE_STATUS);

// 输出值：
// - 未开始: 3
// - 已到课: 2  
// - 未到课: 1
```

### Go Snippet (有问题的实现)
```go
// Go 常量定义 (consts/common.go 第53-55行)
const (
    UndoneStatus  = 0 // 未完成状态
    SuccessStatus = 1 // 成功状态
    WaitStatus    = 2 // 等待状态
)

// Go 状态判断逻辑 (lesson.go 第1805-1815行)
if int64(lessonInfo.StartTime) > currentTime {
    attendStatus = consts.WaitStatus      // 返回 2
} else {
    if isAttend > 0 {
        attendStatus = consts.SuccessStatus  // 返回 1
    } else {
        attendStatus = consts.UndoneStatus   // 返回 0
    }
}

// 输出值：
// - 未开始: 2 (应该是3)
// - 已到课: 1 (应该是2)
// - 未到课: 0 (应该是1)
```

## 数据一致性影响
- **PHP 输出**: 未开始=3, 已到课=2, 未到课=1
- **Go 输出**: 未开始=2, 已到课=1, 未到课=0
- **结果**: 所有状态值都不匹配，前端显示会完全错误

## 根本原因分析
Go 实现使用了错误的常量定义。PHP 中使用的是 `Service_Page_DeskV1_Student_PerformanceV1` 类中定义的常量值，而 Go 中使用了 `consts/common.go` 中定义的不同常量值。

## 建议修复方案 (Suggested Fix)
**方案1：修改 Go 常量值（推荐）**
```go
// 修改 deskcrm/consts/common.go 中的常量定义
const (
    UndoneStatus  = 1 // 未完成状态 (对应PHP的UNDONE_STATUS)
    SuccessStatus = 2 // 成功状态 (对应PHP的SUCCESS_STATUS)  
    WaitStatus    = 3 // 等待状态 (对应PHP的WAIT_STATUS)
)
```

**方案2：创建LPC专用常量**
```go
// 在 deskcrm/consts/ 下创建新文件 lpc.go
const (
    LpcUndoneStatus  = 1 // 对应PHP的UNDONE_STATUS
    LpcSuccessStatus = 2 // 对应PHP的SUCCESS_STATUS
    LpcWaitStatus    = 3 // 对应PHP的WAIT_STATUS
)

// 然后在 GetLpcAttendStatus 中使用这些常量
```

## 影响范围
需要检查所有使用这些常量的LPC相关函数：
- `GetLpcAttendStatus`
- `GetLpcFinishStatus` 
- `GetLpcPlayStatus`
- 其他可能使用相同常量的LPC函数

## 验证状态
❌ **验证失败** - 需要修复常量值不匹配问题后重新验证

## 优先级
**紧急** - 影响所有LPC状态数据的正确性，必须立即修复
