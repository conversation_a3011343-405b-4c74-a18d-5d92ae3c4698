# QA验证失败报告 - GetLpcFinishStatus

## 函数名 (FuncName)
`GetLpcFinishStatus`

## 问题描述 (Description)
Go 实现中的状态常量值与 PHP 实现不匹配，导致输出的状态值完全不同。这是与 `GetLpcAttendStatus` 相同的严重数据一致性问题，会导致前端或下游系统接收到错误的完课状态值。

## 复现路径/代码片段 (Reproduce Steps/Code Snippet)

### PHP Snippet (正确实现)
```php
// PHP 常量定义 (PerformanceV1.php 第23-25行)
const UNDONE_STATUS  = 1;  // 未完成
const SUCCESS_STATUS = 2;  // 已完成  
const WAIT_STATUS    = 3;  // 未开始

// PHP 状态判断逻辑 (PerformanceV1.php 第2077行)
return $item['finishStatus'] = $lessonStartTime > time() ? self::WAIT_STATUS : ($isFinish > 0 ? self::SUCCESS_STATUS : self::UNDONE_STATUS);

// 输出值：
// - 未开始: 3
// - 已完课: 2  
// - 未完课: 1
```

### Go Snippet (有问题的实现)
```go
// Go 常量定义 (consts/common.go 第53-55行)
const (
    UndoneStatus  = 0 // 未完成状态
    SuccessStatus = 1 // 成功状态
    WaitStatus    = 2 // 等待状态
)

// Go 状态判断逻辑 (lesson.go 第1872-1882行)
if int64(lessonInfo.StartTime) > currentTime {
    finishStatus = consts.WaitStatus      // 返回 2
} else {
    if isFinish > 0 {
        finishStatus = consts.SuccessStatus  // 返回 1
    } else {
        finishStatus = consts.UndoneStatus   // 返回 0
    }
}

// 输出值：
// - 未开始: 2 (应该是3)
// - 已完课: 1 (应该是2)
// - 未完课: 0 (应该是1)
```

## 数据一致性影响
- **PHP 输出**: 未开始=3, 已完课=2, 未完课=1
- **Go 输出**: 未开始=2, 已完课=1, 未完课=0
- **结果**: 所有完课状态值都不匹配，前端显示会完全错误

## 根本原因分析
与 `GetLpcAttendStatus` 相同的问题：Go 实现使用了错误的常量定义。PHP 中使用的是 `Service_Page_DeskV1_Student_PerformanceV1` 类中定义的常量值，而 Go 中使用了 `consts/common.go` 中定义的不同常量值。

## 业务逻辑验证
✅ **逻辑等价性**: 完全一致
- 都检查章节开始时间判断是否未开始
- 都调用相应的工具函数判断完课状态
- 都根据播放类型区分直播、录播、AI互动的完课判断

✅ **数据源一致性**: 完全一致
- `getLessonIsFinished` ↔ `Duxuesc_Utils::getLessonIsFinished`
- 都根据播放类型获取对应的完课字段

❌ **输出格式一致性**: 常量值完全不匹配

## 建议修复方案 (Suggested Fix)
**与 GetLpcAttendStatus 统一修复**：
```go
// 修改 deskcrm/consts/common.go 中的常量定义
const (
    UndoneStatus  = 1 // 未完成状态 (对应PHP的UNDONE_STATUS)
    SuccessStatus = 2 // 成功状态 (对应PHP的SUCCESS_STATUS)  
    WaitStatus    = 3 // 等待状态 (对应PHP的WAIT_STATUS)
)
```

## 影响范围
这是一个系统性问题，影响所有使用这些常量的LPC相关函数：
- ❌ `GetLpcAttendStatus` (已发现)
- ❌ `GetLpcFinishStatus` (当前函数)
- ⚠️ `GetLpcPlayStatus` (需要检查)
- ⚠️ 其他可能使用相同常量的LPC函数

## 验证状态
❌ **验证失败** - 需要修复常量值不匹配问题后重新验证

## 优先级
**紧急** - 与 GetLpcAttendStatus 相同的系统性问题，影响所有LPC状态数据的正确性
