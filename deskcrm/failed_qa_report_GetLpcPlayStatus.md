# QA验证失败报告 - GetLpcPlayStatus

## 函数名 (FuncName)
`GetLpcPlayStatus`

## 问题描述 (Description)
Go 实现中的状态常量值与 PHP 实现不匹配，导致输出的状态值完全不同。这是与 `GetLpcAttendStatus` 和 `GetLpcFinishStatus` 相同的严重数据一致性问题，进一步确认了这是一个系统性的常量值不匹配问题。

## 复现路径/代码片段 (Reproduce Steps/Code Snippet)

### PHP Snippet (正确实现)
```php
// PHP 常量定义 (PerformanceV1.php 第23-25行)
const UNDONE_STATUS  = 1;  // 未完成
const SUCCESS_STATUS = 2;  // 已完成  
const WAIT_STATUS    = 3;  // 未开始

// PHP 状态判断逻辑 (PerformanceV1.php 第2092-2102行)
if ($lessonStartTime > time()) {
    return self::WAIT_STATUS;  // 返回 3
}
return $item['playStatus'] = $playbackTime >= 300 ? self::SUCCESS_STATUS : self::UNDONE_STATUS;
// 回放时长>=300: 返回 2
// 回放时长<300: 返回 1

// 输出值：
// - 未开始: 3
// - 已回放: 2  
// - 未回放: 1
```

### Go Snippet (有问题的实现)
```go
// Go 常量定义 (consts/common.go 第53-55行)
const (
    UndoneStatus  = 0 // 未完成状态
    SuccessStatus = 1 // 成功状态
    WaitStatus    = 2 // 等待状态
)

// Go 状态判断逻辑 (lesson.go 第1939-1949行)
if int64(lessonInfo.StartTime) > currentTime {
    playStatus = consts.WaitStatus      // 返回 2
} else {
    if lu.PlaybackTotalTime >= 300 {
        playStatus = consts.SuccessStatus  // 返回 1
    } else {
        playStatus = consts.UndoneStatus   // 返回 0
    }
}

// 输出值：
// - 未开始: 2 (应该是3)
// - 已回放: 1 (应该是2)
// - 未回放: 0 (应该是1)
```

## 数据一致性影响
- **PHP 输出**: 未开始=3, 已回放=2, 未回放=1
- **Go 输出**: 未开始=2, 已回放=1, 未回放=0
- **结果**: 所有回放状态值都不匹配，前端显示会完全错误

## 系统性问题确认
这是第三个发现相同问题的LPC函数，确认了这是一个系统性的常量值不匹配问题：
- ❌ `GetLpcAttendStatus` (到课状态)
- ❌ `GetLpcFinishStatus` (完课状态)
- ❌ `GetLpcPlayStatus` (回放状态)

## 业务逻辑验证
✅ **逻辑等价性**: 完全一致
- 都检查章节开始时间判断是否未开始
- 都使用300秒作为回放时长阈值
- 都从LU数据获取回放时长

✅ **数据源一致性**: 完全一致
- PHP: `$this->lpcLUData[$lessonId]['playback_time']`
- Go: `lu.PlaybackTotalTime`

✅ **架构合规性**: 正确使用 `GetInstanceData` 获取数据

❌ **输出格式一致性**: 常量值完全不匹配

## 建议修复方案 (Suggested Fix)
**统一修复所有LPC函数**：
```go
// 修改 deskcrm/consts/common.go 中的常量定义
const (
    UndoneStatus  = 1 // 未完成状态 (对应PHP的UNDONE_STATUS)
    SuccessStatus = 2 // 成功状态 (对应PHP的SUCCESS_STATUS)  
    WaitStatus    = 3 // 等待状态 (对应PHP的WAIT_STATUS)
)
```

## 影响范围
系统性问题，影响所有使用这些常量的LPC相关函数：
- ❌ `GetLpcAttendStatus` (已确认)
- ❌ `GetLpcFinishStatus` (已确认)
- ❌ `GetLpcPlayStatus` (已确认)
- ⚠️ 其他可能使用相同常量的LPC函数

## 验证状态
❌ **验证失败** - 需要修复常量值不匹配问题后重新验证

## 优先级
**紧急** - 系统性问题，影响所有LPC状态数据的正确性，必须立即修复
